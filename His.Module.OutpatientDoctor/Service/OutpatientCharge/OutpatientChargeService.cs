using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using His.Module.MedicalTech.Api.Dispose;
using His.Module.MedicalTech.Api.Dispose.Dto;
using His.Module.MedicalTech.Api.Examination;
using His.Module.MedicalTech.Api.Examination.Dto;
using His.Module.MedicalTech.Api.LabTest;
using His.Module.MedicalTech.Api.LabTest.Dto;
using His.Module.OutpatientDoctor.OtherModelEntity;
using His.Module.OutpatientDoctor.Service.OutpatientCharge.Dto;
namespace His.Module.OutpatientDoctor.Service.OutpatientCharge;

/// <summary>
/// 门诊收费服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class OutpatientChargeService(
    SqlSugarRepository<Register> registerRep,
    SqlSugarRepository<ChargeMain> chargeMainRep,
    SqlSugarRepository<ChargeDetail> chargeDetailRep,
    SqlSugarRepository<ViewPrescriptionDetail> viewPrescriptionDetailRep,
    SysDictTypeService sysDictTypeService,
    ILabTestApi labTestApi,
    IExaminationApi examinationApi,
    IDisposeApi disposeApi,
    PrescriptionService prescriptionService
) : IDynamicApiController, ITransient
{

    /// <summary>
    /// 业务类型与字典编码映射
    /// </summary>
    private static readonly Dictionary<string, string> BusinessTypeDictCodeMap = new Dictionary<string, string>
    {
        ["Prescription"] = "OutpatientPrescriptionStatus", ["LabTest"] = "ApplyStatus", ["Examination"] = "ApplyStatus", ["Dispose"] = "ApplyStatus"
    };

    /// <summary>
    /// 分页查询收费记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询处方主表")]
    [ApiDescriptionSettings(Name = "Page")][HttpPost]
    public async Task<SqlSugarPagedList<OutpatientChargeOutput>> Page(PageOutpatientChargeInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var main = await chargeMainRep.AsTenant().QueryableWithAttr<ChargeMain>()
            .LeftJoin<Register>((u, a) => u.RegisterId == a.Id)
            .LeftJoin<FeeCategory>((u, a, b) => a.FeeId == b.Id)
            .LeftJoin<RegCategory>((u, a, b, c) => a.RegCategoryId == c.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), u => u.VisitNo == input.VisitNo)
            .WhereIF(!string.IsNullOrWhiteSpace(input.InvoiceNumber), u => u.InvoiceNumber == input.InvoiceNumber)
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo), u => u.OutpatientNo == input.OutpatientNo)
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), (u, a, b, c) => a.PatientName == input.PatientName)
            .WhereIF(input.BillingDeptId.HasValue, u => u.BillingDeptId == input.BillingDeptId)
            .WhereIF(input.ExecuteDeptId.HasValue, u => u.ExecuteDeptId == input.ExecuteDeptId)
            .WhereIF(input.ChargeTimeRange?.Length == 2,
                u => u.CreateTime >= input.ChargeTimeRange[0] &&
                     u.CreateTime <= input.ChargeTimeRange[1])
            .WhereIF(input.Status.HasValue, u => (int)u.Status == input.Status)
            .Select((u, a, b, c) => new OutpatientChargeOutput
            {
                PatientName = a.PatientName,
                Sex = (int)a.Sex,
                Age = a.Age,
                AgeUnit = a.AgeUnit,
                IdCardNo = a.IdCardNo,
                FeeName = b.Name,
                RegCategoryName = c.Name,
                CreateTime = u.CreateTime
            }, true).ToPagedListAsync(input.Page, input.PageSize);
        if (!main.Items.Any())
            return main;

        var chargeIds = main.Items.Select(u => u.Id).ToList();
        var chargeDetails = await chargeDetailRep.AsTenant().QueryableWithAttr<ChargeDetail>()
            .LeftJoin<ChargeCategory>((u, a) => u.ChargeCategoryId == a.Id)
            .LeftJoin<PayMethod>((u, a, c) => u.PayMethod1Id == a.Id)
            .LeftJoin<PayMethod>((u, a, c, d) => u.PayMethod2Id == a.Id)
            .LeftJoin<SysOrg>((u, a, c, d, e) => u.BillingDeptId == a.Id)
            .LeftJoin<SysOrg>((u, a, c, d, e, f) => u.ExecuteDeptId == a.Id)
            .Where(u => chargeIds.Contains((long)u.ChargeId))
            .Select((u, a, c, d, e, f) => new OutpatientChargeDetailDto
            {
                ChargeCategoryName = a.Name,
                PayMethod1Name = c.Name ?? "其他",
                PayMethod2Name = d.Name,
                BillingDeptName = e.Name,
                ExecuteDeptName = f.Name
            }, true)
            .ToListAsync();

        var detailsDict = chargeDetails
            .GroupBy(d => d.ChargeId)
            .ToDictionary(g => g.Key, g => g.ToList());

        foreach (var item in main.Items)
        {
            if (!detailsDict.TryGetValue(item.Id, out var details))
            {
                item.Details = [];
                continue;
            }

            // 按套餐ID分组处理明细
            var groupedDetails = details
                .GroupBy(d => d.PackId)
                .ToList();

            var processedDetails = new List<OutpatientChargeDetailDto>();

            foreach (var group in groupedDetails)
                if (group.Key.HasValue) // 套餐项目
                {
                    // 获取套餐的第一个项目作为套餐信息
                    var firstPackItem = group.First();
                    firstPackItem.ItemId = firstPackItem.PackId;
                    firstPackItem.ItemCode = firstPackItem.PackCode;
                    firstPackItem.ItemName = firstPackItem.PackName;
                    firstPackItem.Price = firstPackItem.PackPrice;
                    firstPackItem.Quantity = firstPackItem.PackNumber;
                    firstPackItem.Amount = firstPackItem.PackPrice * firstPackItem.PackNumber;
                    firstPackItem.IsPackage = 1;
                    firstPackItem.PackageItems = group.Select(g => new PackageSubItemDto
                    {
                        ItemId = g.ItemId,
                        ItemCode = g.ItemCode,
                        ItemName = g.ItemName,
                        Spec = g.Spec,
                        Unit = g.Unit,
                        Price = g.Price,
                        Quantity = g.Quantity,
                        Amount = g.Amount
                    }).ToList();
                    processedDetails.Add(firstPackItem);
                }
                else // 非套餐项目
                    processedDetails.AddRange(group);

            item.Details = processedDetails;
        }

        return main;
    }

    /// <summary>
    /// 根据门诊流水号查询患者挂号信息
    /// </summary>
    /// <param name="visitNo">门诊流水号</param>
    /// <returns></returns>
    [DisplayName("根据门诊流水号查询患者挂号信息")]
    [ApiDescriptionSettings(Name = "GetRegisterByVisitNo")]
    [HttpGet]
    public async Task<RegisterOutput> GetRegisterByVisitNo([FromQuery] string visitNo)
    {
        return await registerRep.AsTenant().QueryableWithAttr<Register>()
            .LeftJoin<SysOrg>((u, a) => u.DeptId == a.Id)
            .LeftJoin<SysUser>((u, a, b) => u.DoctorId == b.Id)
            .LeftJoin<FeeCategory>((u, a, b, c) => u.FeeId == c.Id)
            .LeftJoin<RegCategory>((u, a, b, c, d) => u.RegCategoryId == d.Id)
            .LeftJoin<MedicalCardInfo>((u, a, b, c, d, e) => u.CardId == e.Id)
            .Where(u => u.VisitNo == visitNo)
            .Select((u, a, b, c, d, e) => new RegisterOutput
            {
                DeptName = a.Name,
                DoctorName = b.RealName,
                FeeName = c.Name,
                RegCategory = d.Name,
                CardNo = e.CardNo
            }, true).FirstAsync();
    }

    /// <summary>
    /// 根据门诊流水号查询所有未收费项目
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>未收费项目列表</returns>
    [DisplayName("根据门诊流水号查询所有未收费项目")]
    [ApiDescriptionSettings(Name = "GetUnchargedItemsByVisitNo")]
    [HttpPost]
    public async Task<UnchargedItemsOutput> GetUnchargedItemsByVisitNo(GetUnchargedItemsInput input)
    {
        var result = new UnchargedItemsOutput();
        // 查询患者基本信息
        var registerInfo = await GetRegisterByVisitNo(input.VisitNo);
        if (registerInfo != null)
        {
            result.VisitId = registerInfo.Id;
            result.VisitNo = registerInfo.VisitNo;
            result.PatientId = registerInfo.PatientId;
            result.PatientName = registerInfo.PatientName;
            result.Sex = registerInfo.Sex.GetEnumDescription();
            result.Age = registerInfo.Age;
            result.AgeUnit = registerInfo.AgeUnit;
        }
        // 查询所有未收费项目明细
        var unchargedDetails = await viewPrescriptionDetailRep.AsQueryable()
            .WhereIF(input.VisitId.HasValue, d => d.VisitId == input.VisitId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), d => d.VisitNo == input.VisitNo)
            .Where(d => d.Status == 1) // 1=未收费
            .OrderBy(d => d.BusinessType)
            .ToListAsync();
        if (unchargedDetails == null || unchargedDetails.Count == 0)
            throw Oops.Oh("未查询到未收费的记录！");

        // 按业务类型分组处理
        var businessTypes = new Dictionary<string, (string name, List<UnchargedModuleItemsDto> list)>
        {
            ["LabTest"] = ("检验", result.LabTestItems = []), ["Examination"] = ("检查", result.ExaminationItems = []), ["Prescription"] = ("处方", result.PrescriptionItems = []), ["Dispose"] = ("处置", result.DisposeItems = [])
        };

        // 处理各业务类型并填充结果
        foreach (var (type, (name, list)) in businessTypes)
            list.AddRange(await ProcessModuleItems(unchargedDetails, type, name));

        // 汇总所有项目
        result.SummaryItems = businessTypes.Values
            .SelectMany(item => item.list)
            .ToList();

        // 计算汇总信息
        result.Summary = CalculateSummary(result);

        return result;

    }


    /// <summary>
    /// 收费 💰
    /// </summary>
    /// <param name="input"></param>
    [DisplayName("收费")]
    [ApiDescriptionSettings(Name = "Charge")]
    [HttpPost]
    [UnitOfWork]
    public async Task Charge(ChargeOutpatientChargeInput input)
    {
        // 发票号
        var invoiceNumber = await chargeMainRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('outpatient_doctor.charge_main_in_num_seq')As varchar),8,'0')");

        // 定义 ApplyType 与对应服务方法的映射
        var chargeActions = new Dictionary<string, Func<long, Task>>
        {
            {
                "Dispose", id => disposeApi.Charge(new ChargeDisposeDto
                {
                    Id = id, InvoiceNumber = invoiceNumber
                })
            },
            {
                "LabTest", id => labTestApi.Charge(new ChargeLabTestDto
                {
                    Id = id, InvoiceNumber = invoiceNumber
                })
            },
            {
                "Examination", id => examinationApi.Charge(new ChargeExaminationDto
                {
                    Id = id, InvoiceNumber = invoiceNumber
                })
            },
            {
                "Prescription", id => prescriptionService.Charge(new PrescriptionChargeInput
                {
                    PrescriptionId = id, InvoiceNumber = invoiceNumber
                })
            }
        };

        // 处理所有申请单的收费
        foreach (var applyDetail in input.BusinessDetails)
            if (chargeActions.TryGetValue(applyDetail.BusinessType, out var chargeAction))
                await chargeAction(applyDetail.Id);
            else
                throw Oops.Oh("无效的业务类型！");
    }


    /// <summary>
    /// 处理模块项目数据
    /// </summary>
    private async Task<List<UnchargedModuleItemsDto>> ProcessModuleItems(List<ViewPrescriptionDetail> allDetails, string businessType, string businessName)
    {
        var moduleDetails = allDetails.Where(d => d.BusinessType == businessType).ToList();
        if (moduleDetails.Count == 0) return [];

        // 按申请单号分组
        var groupedByApplyNo = moduleDetails.GroupBy(d => d.ApplyNo).ToList();

        var result = new List<UnchargedModuleItemsDto>();

        foreach (var group in groupedByApplyNo)
        {
            var firstItem = group.First();
            var details = group.Select(d => new UnchargedItemDetailDto
            {
                DetailId = d.DetailId,
                ItemId = d.ItemId,
                ItemCode = d.ItemCode,
                ItemName = d.ItemName,
                Spec = d.Spec,
                Unit = d.Unit,
                Price = d.Price,
                Quantity = d.Quantity,
                Amount = d.Amount,
                MedicineCode = d.MedicineCode,
                NationalstandardCode = d.NationalstandardCode,
                ChargeCategoryId = d.ChargeCategoryId,
                ChargeCategoryName = d.ChargeCategoryName,
                IsPackage = d.IsPackage,
                SelfPayRatio = d.SelfPayRatio,
                ExecuteDeptId = d.ExecuteDeptId,
                ExecuteDeptName = d.ExecuteDeptName,
                PackageItems = d.PackageItemsJson
            }).ToList();

            var moduleItem = new UnchargedModuleItemsDto
            {
                MainId = firstItem.MainId,
                ApplyNo = firstItem.ApplyNo,
                BusinessType = businessType,
                BusinessName = businessName,
                BillingTime = firstItem.BillingTime,
                BillingDeptId = firstItem.BillingDeptId,
                BillingDeptName = firstItem.BillingDeptName,
                BillingDoctorId = firstItem.BillingDoctorId,
                BillingDoctorName = firstItem.BillingDoctorName,
                ExecuteDeptId = firstItem.ExecuteDeptId,
                ExecuteDeptName = firstItem.ExecuteDeptName,
                Status = firstItem.Status,
                StatusDesc = await GetStatusDescription(firstItem.Status, businessType),
                Details = details,
                TotalAmount = details.Sum(d => d.Amount ?? 0),
                TotalCount = details.Count
            };
            result.Add(moduleItem);
        }

        return result;
    }


    /// <summary>
    /// 获取状态描述
    /// </summary>
    /// <param name="status">状态值</param>
    /// <param name="businessType">业务类型</param>
    /// <returns>状态描述</returns>
    private async Task<string> GetStatusDescription(int status, string businessType)
    {

        // 获取对应的字典编码
        if (!BusinessTypeDictCodeMap.TryGetValue(businessType, out var dictCode)) return $"未知状态({status})";

        // 直接调用系统字典服务（利用系统缓存机制）
        var dictDataList = await sysDictTypeService.GetDataList(new GetDataDictTypeInput
        {
            Code = dictCode
        });

        // 查找匹配的字典项
        var dictItem = dictDataList?.FirstOrDefault(item => item.Value == status.ToString());

        return dictItem?.Label ?? $"未知状态({status})";

    }

    /// <summary>
    /// 计算汇总信息
    /// </summary>
    private UnchargedSummaryDto CalculateSummary(UnchargedItemsOutput result)
    {
        return new UnchargedSummaryDto
        {
            LabTestCount = result.LabTestItems.Sum(x => x.TotalCount),
            LabTestAmount = result.LabTestItems.Sum(x => x.TotalAmount),
            ExaminationCount = result.ExaminationItems.Sum(x => x.TotalCount),
            ExaminationAmount = result.ExaminationItems.Sum(x => x.TotalAmount),
            PrescriptionCount = result.PrescriptionItems.Sum(x => x.TotalCount),
            PrescriptionAmount = result.PrescriptionItems.Sum(x => x.TotalAmount),
            DisposeCount = result.DisposeItems.Sum(x => x.TotalCount),
            DisposeAmount = result.DisposeItems.Sum(x => x.TotalAmount),
            TotalCount = result.LabTestItems.Sum(x => x.TotalCount) +
                         result.ExaminationItems.Sum(x => x.TotalCount) +
                         result.PrescriptionItems.Sum(x => x.TotalCount) +
                         result.DisposeItems.Sum(x => x.TotalCount),
            TotalAmount = result.LabTestItems.Sum(x => x.TotalAmount) +
                          result.ExaminationItems.Sum(x => x.TotalAmount) +
                          result.PrescriptionItems.Sum(x => x.TotalAmount) +
                          result.DisposeItems.Sum(x => x.TotalAmount)
        };
    }

}